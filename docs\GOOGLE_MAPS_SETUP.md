# Configuración de Google Maps API para Maclaren

## 🔑 Clave de API Actual
Tu clave de API: `AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8`

## 📋 Pasos para Configurar la Pantalla de Consentimiento OAuth

### 1. Acceder a Google Cloud Console
1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Selecciona tu proyecto o crea uno nuevo
3. Asegúrate de que las siguientes APIs estén habilitadas:
   - Maps JavaScript API
   - Places API
   - Geocoding API
   - Directions API

### 2. Configurar la Pantalla de Consentimiento OAuth

#### Paso 1: Ir a OAuth consent screen
1. En el menú lateral, ve a **APIs & Services** > **OAuth consent screen**
2. Selecciona **External** (para usuarios externos) o **Internal** (solo para tu organización)
3. Haz clic en **CREATE**

#### Paso 2: Información de la App
Completa los siguientes campos obligatorios:

**Información básica:**
- **App name**: `Maclaren - Ride Sharing`
- **User support email**: Tu email
- **App logo**: Sube el logo de Maclaren (opcional)

**Información de contacto del desarrollador:**
- **Email addresses**: Tu email de contacto

**Dominios autorizados:**
- `localhost` (para desarrollo)
- Tu dominio de producción (cuando lo tengas)

#### Paso 3: Scopes (Alcances)
Para Google Maps, generalmente no necesitas scopes adicionales, pero si usas otras APIs de Google:
1. Haz clic en **ADD OR REMOVE SCOPES**
2. Selecciona los scopes necesarios
3. Guarda los cambios

#### Paso 4: Test Users (Usuarios de Prueba)
Si tu app está en modo "Testing":
1. Agrega emails de usuarios que puedan probar la app
2. Máximo 100 usuarios de prueba

### 3. Configurar Restricciones de API Key

#### Restricciones de Aplicación:
1. Ve a **APIs & Services** > **Credentials**
2. Haz clic en tu API key
3. En **Application restrictions**, selecciona:
   - **Android apps** para Android
   - **iOS apps** para iOS
   - **HTTP referrers** para web

#### Para Android:
- **Package name**: `com.yourcompany.maclaren`
- **SHA-1 certificate fingerprint**: Obtén esto ejecutando:
  ```bash
  keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
  ```

#### Para iOS:
- **Bundle ID**: `com.yourcompany.maclaren`

#### Restricciones de API:
Selecciona solo las APIs que necesitas:
- ✅ Maps JavaScript API
- ✅ Places API
- ✅ Geocoding API
- ✅ Directions API

### 4. Configuración en la App

#### app.json ya configurado:
```json
{
  "expo": {
    "android": {
      "config": {
        "googleMaps": {
          "apiKey": "AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8"
        }
      }
    },
    "ios": {
      "config": {
        "googleMapsApiKey": "AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8"
      }
    }
  }
}
```

### 5. Verificación de Configuración

#### Verificar que las APIs estén habilitadas:
1. Ve a **APIs & Services** > **Library**
2. Busca y verifica que estén habilitadas:
   - Maps JavaScript API ✅
   - Places API ✅
   - Geocoding API ✅
   - Directions API ✅

#### Verificar cuotas y límites:
1. Ve a **APIs & Services** > **Quotas**
2. Revisa los límites de uso diario
3. Configura alertas de facturación si es necesario

### 6. Solución de Problemas Comunes

#### Error: "This API project is not authorized to use this API"
- Verifica que la API esté habilitada en tu proyecto
- Asegúrate de usar la clave correcta

#### Error: "The provided API key is invalid"
- Verifica que la clave esté correctamente copiada
- Revisa las restricciones de la API key

#### Error: "REQUEST_DENIED"
- Verifica las restricciones de aplicación
- Asegúrate de que el package name/bundle ID coincida

### 7. Monitoreo y Facturación

#### Configurar alertas de facturación:
1. Ve a **Billing** > **Budgets & alerts**
2. Crea un presupuesto mensual
3. Configura alertas cuando alcances ciertos porcentajes

#### Monitorear uso:
1. Ve a **APIs & Services** > **Quotas**
2. Revisa el uso diario de cada API
3. Optimiza las llamadas si es necesario

## 🚀 Estado Actual
- ✅ API Key configurada
- ✅ Configuración en app.json
- ✅ Configuración centralizada en `src/config/googleMaps.js`
- ⏳ Pantalla de consentimiento OAuth (pendiente)
- ⏳ Restricciones de API Key (recomendado)

## 📞 Soporte
Si tienes problemas con la configuración, revisa:
1. [Documentación oficial de Google Maps](https://developers.google.com/maps/documentation)
2. [Guía de Expo para Google Maps](https://docs.expo.dev/versions/latest/sdk/map-view/)
3. Los logs de la consola para errores específicos
