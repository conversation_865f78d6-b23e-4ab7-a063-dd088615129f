// Configuración de Google Maps API
export const GOOGLE_MAPS_CONFIG = {
  // Tu clave de API de Google Maps
  API_KEY: 'AIzaSyBNNWiDg5hASN4_A8KEY0MyEIjspNQMsw8',
  
  // Configuración para Google Places API
  PLACES_CONFIG: {
    language: 'es',
    components: 'country:co', // Limitar búsquedas a Colombia
    types: 'establishment', // Tipos de lugares a buscar
    radius: 50000, // Radio de búsqueda en metros (50km)
  },
  
  // Configuración del mapa
  MAP_CONFIG: {
    initialRegion: {
      latitude: 4.6097, // Bogotá, Colombia
      longitude: -74.0817,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    },
    mapType: 'standard',
    showsUserLocation: true,
    showsMyLocationButton: false,
    showsTraffic: true,
    showsBuildings: true,
    showsCompass: false,
    showsScale: false,
  },
  
  // Configuración de geocodificación
  GEOCODING_CONFIG: {
    language: 'es',
    region: 'co', // Colombia
  },
  
  // URLs de la API
  API_URLS: {
    geocoding: 'https://maps.googleapis.com/maps/api/geocode/json',
    places: 'https://maps.googleapis.com/maps/api/place/nearbysearch/json',
    directions: 'https://maps.googleapis.com/maps/api/directions/json',
  }
};

// Función para validar la configuración de la API
export const validateGoogleMapsConfig = () => {
  if (!GOOGLE_MAPS_CONFIG.API_KEY) {
    console.warn('⚠️ Google Maps API Key no configurada');
    return false;
  }
  
  if (GOOGLE_MAPS_CONFIG.API_KEY.includes('YOUR_API_KEY')) {
    console.warn('⚠️ Debes reemplazar YOUR_API_KEY con tu clave real de Google Maps');
    return false;
  }
  
  console.log('✅ Configuración de Google Maps válida');
  return true;
};

// Función para obtener la URL de geocodificación
export const getGeocodingUrl = (address) => {
  const { API_KEY, API_URLS, GEOCODING_CONFIG } = GOOGLE_MAPS_CONFIG;
  const encodedAddress = encodeURIComponent(address);
  
  return `${API_URLS.geocoding}?address=${encodedAddress}&key=${API_KEY}&language=${GEOCODING_CONFIG.language}&region=${GEOCODING_CONFIG.region}`;
};

// Función para obtener la URL de búsqueda de lugares
export const getPlacesUrl = (location, query) => {
  const { API_KEY, API_URLS, PLACES_CONFIG } = GOOGLE_MAPS_CONFIG;
  const { latitude, longitude } = location;
  
  return `${API_URLS.places}?location=${latitude},${longitude}&radius=${PLACES_CONFIG.radius}&keyword=${encodeURIComponent(query)}&key=${API_KEY}&language=${PLACES_CONFIG.language}`;
};

// Función para obtener la URL de direcciones
export const getDirectionsUrl = (origin, destination) => {
  const { API_KEY, API_URLS, GEOCODING_CONFIG } = GOOGLE_MAPS_CONFIG;
  const originStr = `${origin.latitude},${origin.longitude}`;
  const destinationStr = `${destination.latitude},${destination.longitude}`;
  
  return `${API_URLS.directions}?origin=${originStr}&destination=${destinationStr}&key=${API_KEY}&language=${GEOCODING_CONFIG.language}`;
};

export default GOOGLE_MAPS_CONFIG;
