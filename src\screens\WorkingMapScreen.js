import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, Dimensions, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Button, Card, Surface } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useUser } from '../context/UserContext';
import { useAuth } from '../context/AuthContext';

const { width, height } = Dimensions.get('window');

const WorkingMapScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { userMode, currentTrip, startTrip } = useUser();
  const [selectedDestination, setSelectedDestination] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);

  // Ubicación fija para que siempre funcione
  const location = {
    latitude: 4.6097,
    longitude: -74.0817,
  };

  const handleMapPress = (event) => {
    const { locationX, locationY } = event.nativeEvent;
    const mapWidth = 300;
    const mapHeight = 300;
    
    const xPercent = Math.max(10, Math.min(90, (locationX / mapWidth) * 100));
    const yPercent = Math.max(10, Math.min(90, (locationY / mapHeight) * 100));
    
    if (userMode === 'passenger') {
      setSelectedDestination({ x: xPercent, y: yPercent });
      Alert.alert(
        'Destino seleccionado 📍',
        '¿Quieres solicitar un viaje a este destino?',
        [
          { text: 'Cancelar', style: 'cancel' },
          { 
            text: 'Solicitar viaje', 
            onPress: () => handleRequestTrip(xPercent, yPercent)
          }
        ]
      );
    } else {
      Alert.alert('Información', 'Como conductor, puedes ver los viajes disponibles en esta área');
    }
  };

  const handleRequestTrip = (x, y) => {
    const mockTrip = {
      _id: 'demo-trip-' + Date.now(),
      pickupLocation: {
        latitude: location.latitude,
        longitude: location.longitude,
        address: 'Tu ubicación actual'
      },
      destination: {
        latitude: location.latitude + 0.01,
        longitude: location.longitude + 0.01,
        address: 'Destino seleccionado'
      },
      offeredPrice: 15000,
      passengerNotes: 'Viaje solicitado desde el mapa',
      passenger: {
        firstName: 'Juan',
        lastName: 'Pérez',
        phone: '+57 ************'
      },
      status: 'accepted'
    };
    
    startTrip(mockTrip);
    
    Alert.alert(
      'Viaje creado ✅',
      'Un conductor ha aceptado tu viaje. ¡Prepárate!',
      [
        {
          text: 'Ver viaje',
          onPress: () => navigation.navigate('ActiveTrip')
        }
      ]
    );
  };

  const handleDriverAction = () => {
    const mockTrip = {
      _id: 'demo-driver-trip-' + Date.now(),
      pickupLocation: {
        latitude: location.latitude + 0.005,
        longitude: location.longitude + 0.005,
        address: 'Centro Comercial Andino'
      },
      destination: {
        latitude: location.latitude + 0.015,
        longitude: location.longitude + 0.015,
        address: 'Aeropuerto El Dorado'
      },
      offeredPrice: 25000,
      passengerNotes: 'Tengo equipaje, por favor ayúdame',
      passenger: {
        firstName: 'María',
        lastName: 'González',
        phone: '+57 ************'
      },
      status: 'accepted'
    };

    Alert.alert(
      'Viaje encontrado 🚗',
      `Nuevo viaje disponible por $${mockTrip.offeredPrice}\n\n¿Aceptar este viaje?`,
      [
        { text: 'Rechazar', style: 'cancel' },
        {
          text: 'Aceptar',
          onPress: () => {
            startTrip(mockTrip);
            Alert.alert(
              'Viaje aceptado ✅',
              'Dirígete al punto de recogida. ¡Buen viaje!',
              [
                {
                  text: 'Iniciar navegación',
                  onPress: () => navigation.navigate('ActiveTrip')
                }
              ]
            );
          }
        }
      ]
    );
  };

  const getCurrentLocation = () => {
    Alert.alert(
      'Ubicación actualizada 📍',
      'Tu ubicación ha sido actualizada correctamente.',
      [{ text: 'OK' }]
    );
  };



  return (
    <View style={styles.container}>
      {/* Header */}
      <Surface style={styles.headerContainer} elevation={4}>
        <LinearGradient
          colors={userMode === 'driver' ? ['#4CAF50', '#45a049'] : ['#ff6b6b', '#ee5a24']}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <Ionicons
              name={userMode === 'driver' ? 'car-sport' : 'person'}
              size={24}
              color="white"
            />
            <Text style={styles.headerText}>
              {userMode === 'driver' ? 'Modo Conductor 🚗' : 'Modo Pasajero 🚶‍♂️'}
            </Text>
          </View>
        </LinearGradient>
      </Surface>

      {/* Mapa Interactivo */}
      <View style={styles.mapContainer}>
        <View style={styles.mapHeader}>
          <Text style={styles.mapTitle}>🗺️ Mapa de Maclaren</Text>
          <Text style={styles.mapSubtitle}>
            {hasPermission ? '📍 Ubicación real' : '🏙️ Bogotá, Colombia'}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={styles.interactiveMap}
          onPress={handleMapPress}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#4CAF50', '#45a049']}
            style={styles.mapBackground}
          >
            {/* Grid de calles */}
            <View style={styles.mapGrid}>
              {[...Array(8)].map((_, i) => (
                <View key={`h-${i}`} style={[styles.street, styles.horizontalStreet, { top: `${i * 12}%` }]} />
              ))}
              {[...Array(6)].map((_, i) => (
                <View key={`v-${i}`} style={[styles.street, styles.verticalStreet, { left: `${i * 16}%` }]} />
              ))}
            </View>

            {/* Marcador de ubicación actual */}
            <View style={[styles.locationMarker, styles.currentLocation]}>
              <Ionicons name="radio-button-on" size={20} color="#ff6b6b" />
              <Text style={styles.markerLabel}>Tu ubicación</Text>
            </View>

            {/* Marcadores de ejemplo */}
            <View style={[styles.locationMarker, { top: '30%', left: '60%' }]}>
              <Ionicons name="car" size={16} color="#2196F3" />
              <Text style={styles.markerLabel}>Conductor</Text>
            </View>

            <View style={[styles.locationMarker, { top: '70%', left: '25%' }]}>
              <Ionicons name="location" size={16} color="#FF9800" />
              <Text style={styles.markerLabel}>Destino</Text>
            </View>

            {/* Destino seleccionado */}
            {selectedDestination && (
              <View style={[styles.locationMarker, { 
                top: `${selectedDestination.y}%`, 
                left: `${selectedDestination.x}%`,
                backgroundColor: '#ff6b6b'
              }]}>
                <Ionicons name="flag" size={16} color="white" />
                <Text style={[styles.markerLabel, { color: 'white' }]}>Tu destino</Text>
              </View>
            )}

            {/* Instrucciones */}
            <View style={styles.mapInteraction}>
              <Text style={styles.interactionText}>
                {userMode === 'passenger' 
                  ? '👆 Toca en el mapa para seleccionar destino'
                  : '🚗 Conductores disponibles en el área'
                }
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* Botones de acción */}
      <View style={styles.actionContainer}>
        <Button
          mode="contained"
          onPress={userMode === 'passenger' ? () => Alert.alert('Info', 'Toca en el mapa para seleccionar destino') : handleDriverAction}
          style={styles.actionButton}
          buttonColor={userMode === 'driver' ? '#4CAF50' : '#ff6b6b'}
          icon={userMode === 'passenger' ? 'map-marker' : 'car-search'}
        >
          {userMode === 'passenger' ? 'Seleccionar destino en mapa' : 'Buscar viajes disponibles'}
        </Button>

        <Button
          mode="outlined"
          onPress={getCurrentLocation}
          style={styles.actionButton}
          textColor={userMode === 'driver' ? '#4CAF50' : '#ff6b6b'}
          icon="refresh"
        >
          Actualizar ubicación
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
  },
  loadingGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 20,
    marginBottom: 10,
  },
  loadingText: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    marginTop: 20,
  },
  headerContainer: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: 'white',
  },
  mapHeader: {
    backgroundColor: 'white',
    padding: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  mapTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  mapSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  interactiveMap: {
    flex: 1,
    position: 'relative',
  },
  mapBackground: {
    flex: 1,
    position: 'relative',
  },
  mapGrid: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  street: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  horizontalStreet: {
    height: 2,
    left: 0,
    right: 0,
  },
  verticalStreet: {
    width: 2,
    top: 0,
    bottom: 0,
  },
  locationMarker: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  currentLocation: {
    top: '50%',
    left: '40%',
    transform: [{ translateX: -25 }, { translateY: -25 }],
  },
  markerLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 2,
  },
  mapInteraction: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    padding: 12,
  },
  interactionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  actionContainer: {
    padding: 16,
    gap: 10,
  },
  actionButton: {
    borderRadius: 12,
  },
});

export default WorkingMapScreen;
