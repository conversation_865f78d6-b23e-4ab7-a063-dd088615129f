import { createApiInstance, apiRequest, OFFLINE_DATA, OfflineStorage } from '../config/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

const api = createApiInstance();

export const tripService = {
  async createTripRequest(tripData) {
    // Obtener información del usuario actual
    const userToken = await AsyncStorage.getItem('userToken');
    const userDataStr = await AsyncStorage.getItem('userData');
    const userData = userDataStr ? JSON.parse(userDataStr) : OFFLINE_DATA.passengerUser;

    const newTrip = {
      _id: 'offline-trip-' + Date.now(),
      pickupLocation: tripData.pickupLocation,
      destination: tripData.destination,
      offeredPrice: tripData.offeredPrice,
      passengerNotes: tripData.notes,
      status: 'pending',
      passenger: {
        id: userData.id,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
      },
      createdAt: new Date().toISOString(),
    };

    const result = await apiRequest(
      () => api.post('/trips/request', {
        pickupLocation: tripData.pickupLocation,
        destination: tripData.destination,
        offeredPrice: tripData.offeredPrice,
        passengerNotes: tripData.notes,
      }),
      {
        trip: newTrip,
      },
      'Error al crear solicitud de viaje'
    );

    if (result.success) {
      // Si estamos en modo offline, guardar el viaje localmente
      if (result.isOffline) {
        await OfflineStorage.saveCreatedTrip(result.data.trip);
        console.log('🚗 Viaje creado y guardado localmente');
      }

      return {
        success: true,
        trip: result.data.trip,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async getAvailableTrips(driverLocation) {
    const result = await apiRequest(
      () => api.get('/trips/available', {
        params: {
          latitude: driverLocation.latitude,
          longitude: driverLocation.longitude,
          radius: 10, // 10 km radius
        },
      }),
      null, // No usar datos estáticos, usar el sistema de almacenamiento
      'Error al obtener viajes disponibles'
    );

    if (result.success) {
      return {
        success: true,
        trips: result.data.trips,
        isOffline: result.isOffline,
      };
    } else {
      // En caso de error, usar el sistema de almacenamiento local
      console.log('📱 Obteniendo viajes desde almacenamiento local');
      const availableTrips = await OfflineStorage.getAvailableTrips();

      return {
        success: true,
        trips: availableTrips,
        isOffline: true,
      };
    }
  },

  async acceptTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/accept`);

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Accept trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al aceptar viaje',
      };
    }
  },

  async startTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/start`);

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Start trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al iniciar viaje',
      };
    }
  },

  async completeTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/complete`);

      return {
        success: true,
        trip: response.data.trip,
        commission: response.data.commission,
      };
    } catch (error) {
      console.error('Complete trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al completar viaje',
      };
    }
  },

  async cancelTrip(tripId, reason) {
    try {
      const response = await api.post(`/trips/${tripId}/cancel`, {
        reason,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Cancel trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al cancelar viaje',
      };
    }
  },

  async updateDriverLocation(tripId, location) {
    try {
      const response = await api.post(`/trips/${tripId}/location`, {
        latitude: location.latitude,
        longitude: location.longitude,
      });

      return {
        success: true,
      };
    } catch (error) {
      console.error('Update driver location error:', error);
      return {
        success: false,
        error: 'Error al actualizar ubicación',
      };
    }
  },

  async getTripHistory(userId) {
    const result = await apiRequest(
      () => api.get(`/trips/history/${userId}`),
      {
        trips: OFFLINE_DATA.tripHistory,
      },
      'Error al obtener historial de viajes'
    );

    if (result.success) {
      return {
        success: true,
        trips: result.data.trips,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },
};
