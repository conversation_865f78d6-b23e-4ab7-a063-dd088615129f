import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para agregar el token de autenticación
api.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('userToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const tripService = {
  async createTripRequest(tripData) {
    try {
      const response = await api.post('/trips/request', {
        pickupLocation: tripData.pickupLocation,
        destination: tripData.destination,
        offeredPrice: tripData.offeredPrice,
        passengerNotes: tripData.notes,
      });

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Create trip request error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al crear solicitud de viaje',
      };
    }
  },

  async getAvailableTrips(driverLocation) {
    try {
      const response = await api.get('/trips/available', {
        params: {
          latitude: driverLocation.latitude,
          longitude: driverLocation.longitude,
          radius: 10, // 10 km radius
        },
      });

      return {
        success: true,
        trips: response.data.trips,
      };
    } catch (error) {
      console.error('Get available trips error:', error);
      console.warn('Usando datos de respaldo para viajes disponibles');
      return {
        success: true,
        trips: [
          {
            _id: 'offline-trip-1',
            pickupLocation: {
              latitude: 4.6097,
              longitude: -74.0817,
              address: 'Centro de Bogotá'
            },
            destination: {
              latitude: 4.6351,
              longitude: -74.0703,
              address: 'Zona Rosa'
            },
            offeredPrice: 15000,
            status: 'pending',
            passenger: {
              firstName: 'Juan',
              lastName: 'Pérez',
              phone: '+57 ************'
            }
          }
        ],
      };
    }
  },

  async acceptTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/accept`);

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Accept trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al aceptar viaje',
      };
    }
  },

  async startTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/start`);

      return {
        success: true,
        trip: response.data.trip,
      };
    } catch (error) {
      console.error('Start trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al iniciar viaje',
      };
    }
  },

  async completeTrip(tripId) {
    try {
      const response = await api.post(`/trips/${tripId}/complete`);

      return {
        success: true,
        trip: response.data.trip,
        commission: response.data.commission,
      };
    } catch (error) {
      console.error('Complete trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al completar viaje',
      };
    }
  },

  async cancelTrip(tripId, reason) {
    try {
      const response = await api.post(`/trips/${tripId}/cancel`, {
        reason,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Cancel trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al cancelar viaje',
      };
    }
  },

  async updateDriverLocation(tripId, location) {
    try {
      const response = await api.post(`/trips/${tripId}/location`, {
        latitude: location.latitude,
        longitude: location.longitude,
      });

      return {
        success: true,
      };
    } catch (error) {
      console.error('Update driver location error:', error);
      return {
        success: false,
        error: 'Error al actualizar ubicación',
      };
    }
  },

  async getTripHistory(userId) {
    try {
      const response = await api.get(`/trips/history/${userId}`);

      return {
        success: true,
        trips: response.data.trips,
      };
    } catch (error) {
      console.error('Get trip history error:', error);
      console.warn('Usando datos de respaldo para historial de viajes');
      return {
        success: true,
        trips: [
          {
            _id: 'offline-history-1',
            pickupLocation: { address: 'Centro de Bogotá' },
            destination: { address: 'Zona Rosa' },
            offeredPrice: 15000,
            status: 'completed',
            createdAt: new Date().toISOString(),
            completedAt: new Date().toISOString()
          },
          {
            _id: 'offline-history-2',
            pickupLocation: { address: 'Chapinero' },
            destination: { address: 'Suba' },
            offeredPrice: 20000,
            status: 'completed',
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            completedAt: new Date(Date.now() - 86400000).toISOString()
          }
        ],
      };
    }
  },
};
