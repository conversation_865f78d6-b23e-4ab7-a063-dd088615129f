import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuración de la API
export const API_CONFIG = {
  // Cambia esta URL por la de tu backend cuando esté disponible
  BASE_URL: 'http://192.168.0.3:3000/api',
  TIMEOUT: 5000, // Reducido a 5 segundos para fallar más rápido
  RETRY_ATTEMPTS: 2,
  OFFLINE_MODE: true, // Habilitar modo offline por defecto
};

// Crear instancia de axios con configuración mejorada
export const createApiInstance = () => {
  const api = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Interceptor para agregar token de autenticación
  api.interceptors.request.use(
    async (config) => {
      try {
        const token = await AsyncStorage.getItem('userToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.warn('Error al obtener token:', error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Interceptor para manejar respuestas y errores
  api.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      console.warn('API Error:', error.message);
      
      // Si es un error de red o timeout, activar modo offline
      if (error.code === 'ECONNABORTED' || error.code === 'NETWORK_ERROR' || !error.response) {
        console.log('🔄 Activando modo offline debido a error de conexión');
      }
      
      return Promise.reject(error);
    }
  );

  return api;
};

// Función helper para manejar peticiones con fallback offline
export const apiRequest = async (requestFn, fallbackData = null, errorMessage = 'Error de conexión') => {
  try {
    const result = await requestFn();
    return {
      success: true,
      data: result.data,
      isOffline: false,
    };
  } catch (error) {
    console.warn(`${errorMessage}:`, error.message);
    
    if (API_CONFIG.OFFLINE_MODE && fallbackData !== null) {
      console.log('📱 Usando datos offline');
      return {
        success: true,
        data: fallbackData,
        isOffline: true,
      };
    }
    
    return {
      success: false,
      error: error.response?.data?.message || errorMessage,
      isOffline: false,
    };
  }
};

// Función para verificar conectividad
export const checkApiConnectivity = async () => {
  try {
    const api = createApiInstance();
    await api.get('/health', { timeout: 3000 });
    return true;
  } catch (error) {
    console.log('🔌 API no disponible, usando modo offline');
    return false;
  }
};

// Datos de respaldo para modo offline
export const OFFLINE_DATA = {
  user: {
    id: 'offline-user-123',
    firstName: 'Usuario',
    lastName: 'Demo',
    email: '<EMAIL>',
    phone: '+57 ************',
    userType: 'passenger',
    balance: 50000,
    documentsVerified: true,
  },
  
  trips: [
    {
      _id: 'offline-trip-1',
      pickupLocation: {
        latitude: 4.6097,
        longitude: -74.0817,
        address: 'Centro de Bogotá, Bogotá'
      },
      destination: {
        latitude: 4.6351,
        longitude: -74.0703,
        address: 'Zona Rosa, Bogotá'
      },
      offeredPrice: 15000,
      status: 'pending',
      passenger: {
        firstName: 'Juan',
        lastName: 'Pérez',
        phone: '+57 ************'
      },
      createdAt: new Date().toISOString()
    },
    {
      _id: 'offline-trip-2',
      pickupLocation: {
        latitude: 4.6482,
        longitude: -74.0776,
        address: 'Chapinero, Bogotá'
      },
      destination: {
        latitude: 4.7110,
        longitude: -74.0721,
        address: 'Suba, Bogotá'
      },
      offeredPrice: 20000,
      status: 'pending',
      passenger: {
        firstName: 'María',
        lastName: 'González',
        phone: '+57 ************'
      },
      createdAt: new Date(Date.now() - 300000).toISOString()
    }
  ],
  
  tripHistory: [
    {
      _id: 'offline-history-1',
      pickupLocation: { address: 'Centro de Bogotá' },
      destination: { address: 'Zona Rosa' },
      offeredPrice: 15000,
      status: 'completed',
      createdAt: new Date(Date.now() - 86400000).toISOString(),
      completedAt: new Date(Date.now() - 86400000 + 1800000).toISOString()
    },
    {
      _id: 'offline-history-2',
      pickupLocation: { address: 'Chapinero' },
      destination: { address: 'Suba' },
      offeredPrice: 20000,
      status: 'completed',
      createdAt: new Date(Date.now() - 172800000).toISOString(),
      completedAt: new Date(Date.now() - 172800000 + 2400000).toISOString()
    }
  ],
  
  transactions: [
    {
      id: 'offline-txn-1',
      type: 'recharge',
      amount: 50000,
      date: new Date(Date.now() - 86400000).toISOString(),
      description: 'Recarga inicial (offline)'
    },
    {
      id: 'offline-txn-2',
      type: 'trip',
      amount: -15000,
      date: new Date(Date.now() - 43200000).toISOString(),
      description: 'Viaje completado (offline)'
    }
  ]
};
