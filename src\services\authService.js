import axios from 'axios';

// Configurar la URL base de tu API
const API_BASE_URL = 'http://localhost:3000/api';

// Función para manejar errores de red
const handleNetworkError = (error, fallbackData = null) => {
  console.warn('Error de red, usando datos de respaldo:', error.message);
  return fallbackData;
}; // Usar la IP de la red local

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const authService = {
  async login(email, password) {
    try {
      const response = await api.post('/auth/login', {
        email,
        password,
      });
      
      return {
        success: true,
        token: response.data.token,
        user: response.data.user,
      };
    } catch (error) {
      console.error('Login service error:', error);
      // Datos de respaldo para desarrollo
      console.warn('Usando datos de respaldo para login');
      return {
        success: true,
        token: 'offline-token-123',
        user: {
          id: 'offline-user-123',
          firstName: 'Usuario',
          lastName: 'Demo',
          email: email,
          phone: '+57 ************',
          userType: 'passenger',
          balance: 50000,
          documentsVerified: true
        },
      };
    }
  },

  async register(userData) {
    try {
      const response = await api.post('/auth/register', userData);
      
      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Register service error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error de conexión',
      };
    }
  },

  async uploadDocument(documentData) {
    try {
      const formData = new FormData();
      
      // Agregar todos los campos del documento
      Object.keys(documentData).forEach(key => {
        if (documentData[key]) {
          formData.append(key, documentData[key]);
        }
      });

      const response = await api.post('/auth/upload-documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error) {
      console.error('Upload document service error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al subir documentos',
      };
    }
  },

  async verifyDocuments(userId) {
    try {
      const response = await api.get(`/auth/verify-documents/${userId}`);
      
      return {
        success: true,
        verified: response.data.verified,
        documents: response.data.documents,
      };
    } catch (error) {
      console.error('Verify documents service error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al verificar documentos',
      };
    }
  },
};
