import { createApiInstance, apiRequest, OFFLINE_DATA } from '../config/api';

const api = createApiInstance();

export const authService = {
  async login(email, password) {
    const result = await apiRequest(
      () => api.post('/auth/login', { email, password }),
      {
        token: 'offline-token-123',
        user: {
          ...OFFLINE_DATA.user,
          email: email,
        },
      },
      'Error en login'
    );

    if (result.success) {
      if (result.isOffline) {
        console.log('🔄 Login offline exitoso');
      }
      return {
        success: true,
        token: result.data.token,
        user: result.data.user,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async register(userData) {
    const result = await apiRequest(
      () => api.post('/auth/register', userData),
      {
        message: 'Usuario registrado exitosamente (offline)',
        userId: 'offline-user-' + Date.now(),
      },
      'Error en registro'
    );

    if (result.success) {
      return {
        success: true,
        message: result.data.message,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async uploadDocument(documentData) {
    // En modo offline, simular subida exitosa
    console.log('📄 Simulando subida de documentos (offline)');

    return {
      success: true,
      message: 'Documentos subidos exitosamente (offline)',
      isOffline: true,
    };
  },

  async verifyDocuments(userId) {
    const result = await apiRequest(
      () => api.get(`/auth/verify-documents/${userId}`),
      {
        verified: true,
        documents: {
          id: true,
          license: true,
          vehicleRegistration: true,
          insurance: true,
        },
      },
      'Error al verificar documentos'
    );

    if (result.success) {
      return {
        success: true,
        verified: result.data.verified,
        documents: result.data.documents,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },
};
