import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para agregar el token de autenticación
api.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('userToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const paymentService = {
  async rechargeBalance(amount = 10) {
    try {
      const response = await api.post('/payments/recharge', {
        amount,
      });

      return {
        success: true,
        newBalance: response.data.balance,
        transactionId: response.data.transactionId,
      };
    } catch (error) {
      console.error('Recharge balance error:', error);
      console.warn('Usando datos de respaldo para recarga');
      return {
        success: true,
        newBalance: 50000 + amount,
        transactionId: `offline-txn-${Date.now()}`,
      };
    }
  },

  async getBalance(userId) {
    try {
      const response = await api.get(`/payments/balance/${userId}`);

      return {
        success: true,
        balance: response.data.balance,
      };
    } catch (error) {
      console.error('Get balance error:', error);
      console.warn('Usando datos de respaldo para balance');
      return {
        success: true,
        balance: 50000,
      };
    }
  },

  async processCommission(tripId, tripAmount) {
    try {
      const commission = tripAmount * 0.10; // 10% de comisión
      
      const response = await api.post('/payments/commission', {
        tripId,
        tripAmount,
        commission,
      });

      return {
        success: true,
        commission: response.data.commission,
        remainingBalance: response.data.remainingBalance,
        needsRecharge: response.data.needsRecharge,
      };
    } catch (error) {
      console.error('Process commission error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al procesar comisión',
      };
    }
  },

  async getTransactionHistory(userId) {
    try {
      const response = await api.get(`/payments/history/${userId}`);

      return {
        success: true,
        transactions: response.data.transactions,
      };
    } catch (error) {
      console.error('Get transaction history error:', error);
      console.warn('Usando datos de respaldo para historial');
      return {
        success: true,
        transactions: [
          {
            id: 'offline-1',
            type: 'recharge',
            amount: 10000,
            date: new Date().toISOString(),
            description: 'Recarga de saldo (offline)'
          },
          {
            id: 'offline-2',
            type: 'trip',
            amount: -5000,
            date: new Date(Date.now() - 86400000).toISOString(),
            description: 'Viaje completado (offline)'
          }
        ],
      };
    }
  },

  async checkBalanceForTrip(userId) {
    try {
      const response = await api.get(`/payments/check-balance/${userId}`);

      return {
        success: true,
        canReceiveTrips: response.data.canReceiveTrips,
        balance: response.data.balance,
        needsRecharge: response.data.needsRecharge,
      };
    } catch (error) {
      console.error('Check balance for trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al verificar saldo',
      };
    }
  },

  // Simulación de proceso de pago (en producción integrarías con un gateway real)
  async processPayment(paymentData) {
    try {
      // Aquí integrarías con un gateway de pagos real como Stripe, PayPal, etc.
      const response = await api.post('/payments/process', paymentData);

      return {
        success: true,
        transactionId: response.data.transactionId,
        status: response.data.status,
      };
    } catch (error) {
      console.error('Process payment error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al procesar pago',
      };
    }
  },
};
