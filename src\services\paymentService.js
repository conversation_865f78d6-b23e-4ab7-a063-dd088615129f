import { createApiInstance, apiRequest, OFFLINE_DATA } from '../config/api';

const api = createApiInstance();

export const paymentService = {
  async rechargeBalance(amount = 10000) {
    const result = await apiRequest(
      () => api.post('/payments/recharge', { amount }),
      {
        balance: OFFLINE_DATA.user.balance + amount,
        transactionId: `offline-txn-${Date.now()}`,
      },
      'Error al recargar saldo'
    );

    if (result.success) {
      return {
        success: true,
        newBalance: result.data.balance,
        transactionId: result.data.transactionId,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async getBalance(userId) {
    const result = await apiRequest(
      () => api.get(`/payments/balance/${userId}`),
      {
        balance: OFFLINE_DATA.user.balance,
      },
      'Error al obtener saldo'
    );

    if (result.success) {
      return {
        success: true,
        balance: result.data.balance,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async processCommission(tripId, tripAmount) {
    try {
      const commission = tripAmount * 0.10; // 10% de comisión
      
      const response = await api.post('/payments/commission', {
        tripId,
        tripAmount,
        commission,
      });

      return {
        success: true,
        commission: response.data.commission,
        remainingBalance: response.data.remainingBalance,
        needsRecharge: response.data.needsRecharge,
      };
    } catch (error) {
      console.error('Process commission error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al procesar comisión',
      };
    }
  },

  async getTransactionHistory(userId) {
    const result = await apiRequest(
      () => api.get(`/payments/history/${userId}`),
      {
        transactions: OFFLINE_DATA.transactions,
      },
      'Error al obtener historial de transacciones'
    );

    if (result.success) {
      return {
        success: true,
        transactions: result.data.transactions,
        isOffline: result.isOffline,
      };
    }

    return {
      success: false,
      error: result.error,
    };
  },

  async checkBalanceForTrip(userId) {
    try {
      const response = await api.get(`/payments/check-balance/${userId}`);

      return {
        success: true,
        canReceiveTrips: response.data.canReceiveTrips,
        balance: response.data.balance,
        needsRecharge: response.data.needsRecharge,
      };
    } catch (error) {
      console.error('Check balance for trip error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al verificar saldo',
      };
    }
  },

  // Simulación de proceso de pago (en producción integrarías con un gateway real)
  async processPayment(paymentData) {
    try {
      // Aquí integrarías con un gateway de pagos real como Stripe, PayPal, etc.
      const response = await api.post('/payments/process', paymentData);

      return {
        success: true,
        transactionId: response.data.transactionId,
        status: response.data.status,
      };
    } catch (error) {
      console.error('Process payment error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Error al procesar pago',
      };
    }
  },
};
